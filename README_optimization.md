# 判決書分析系統 - 成本優化方案

## 專案概述

這是針對您的判決書分析系統的成本優化實作，主要目標是將 500 篇判決書的分析成本從約 $150 降低到 $30-45，節省 70-80% 的費用。

## 核心優化策略

### 1. 兩階段合併調用 (67% 調用次數減少)

**原始方案：** 6 次 API 調用
- extract_case_info (案件資料)
- extract_people (人物資料)
- extract_organizations (組織資料)
- extract_locations (地點資料)
- extract_laws (法條資料)
- extract_events (事件資料)

**優化方案：** 2 次 API 調用
- **第一階段 (extract_phase1):** 案件 + 人物 + 組織
- **第二階段 (extract_phase2):** 地點 + 法條 + 事件

### 2. 模型選擇優化 (60% 成本減少)

- **原始：** GPT-4o ($0.0025/1K input, $0.01/1K output)
- **優化：** GPT-4o-mini ($0.00015/1K input, $0.0006/1K output)

### 3. Prompt 優化

- 更精確的指令減少不必要輸出
- 利用前階段結果作為後階段上下文
- 結構化 Schema 強制規範輸出格式

## 預期成本節省

| 項目 | 原始方案 | 優化方案 | 節省比例 |
|------|----------|----------|----------|
| API 調用次數 | 6 次/篇 | 2 次/篇 | 67% |
| 模型成本 | GPT-4o | GPT-4o-mini | 60% |
| **總成本 (500篇)** | **~$150** | **~$30** | **80%** |

## 檔案結構

```
├── verdict_graph.py          # 主要分析系統 (已優化)
├── verdict_graph_original.py # 原始版本備份
├── test_optimization.py      # 測試腳本
└── README_optimization.md    # 說明文件
```

## 使用方式

### 1. 快速開始 (推薦)

```python
# 使用優化版本處理單個檔案
import asyncio
from verdict_graph import process_judgment_optimized

result = asyncio.run(process_judgment_optimized("sample_500/some_file.json"))
```

### 2. 批量處理

```python
# 批量處理多個檔案
from verdict_graph import batch_process_judgments

file_paths = ["file1.json", "file2.json", "file3.json"]
results = asyncio.run(batch_process_judgments(
    file_paths, 
    use_optimized=True, 
    model="gpt-4o-mini"
))
```

### 3. 成本比較測試

```python
# 執行成本比較測試
from verdict_graph import test_cost_comparison

asyncio.run(test_cost_comparison(num_samples=5))
```

### 4. 執行主程式

```bash
# 互動式執行
python verdict_graph.py

# 或執行測試腳本
python test_optimization.py
```

## 功能特色

### ✅ 成本追蹤
- 即時 API 調用計數
- 預估成本計算
- 處理時間統計

### ✅ 向後相容
- 保留原始 6 階段工作流程
- 可選擇使用優化或原始版本
- 原始功能完全保留

### ✅ 錯誤處理
- 完善的異常處理機制
- 失敗重試策略
- 詳細錯誤日誌

### ✅ 彈性配置
- 可選擇不同 LLM 模型
- 可調整批量處理參數
- 支援自訂工作流程

## 技術細節

### Token 使用量分析

| 模型 | Context Window | 適用情況 |
|------|----------------|----------|
| GPT-4o | 128K tokens | 複雜長文檔 |
| GPT-4o-mini | 128K tokens | ✅ 判決書分析 |
| GPT-3.5-turbo | 16K tokens | 過小，不適用 |

**您的判決書 (10,000字以內)：**
- 約 13,000-15,000 tokens
- 完全適合 GPT-4o-mini 的 128K 限制
- 無需擔心 token 超量問題

### 準確度保障

1. **漸進式驗證：** 小批量測試 → 大批量應用
2. **雙重檢查：** 重要案件可同時使用兩種版本
3. **品質監控：** 定期人工抽檢結果
4. **快速回滾：** 發現問題可立即切回原版本

## 實施建議

### 階段 1：驗證測試 (1-2天)
```bash
# 執行快速測試驗證功能
python test_optimization.py

# 小批量比較測試 (3-5個檔案)
python verdict_graph.py  # 選擇模式 1
```

### 階段 2：小規模應用 (3-5天)
```bash
# 處理 10-20 個檔案驗證準確度
python verdict_graph.py  # 選擇模式 2
```

### 階段 3：全面部署 (1週後)
```bash
# 批量處理全部 500 個檔案
python verdict_graph.py  # 選擇模式 2
```

## 風險控制

### 🛡️ 準確度風險
- **緩解措施：** 保留原始版本作為對照
- **監控指標：** 人物提取重疊率 > 80%
- **應急方案：** 自動回滾機制

### 🛡️ 成本控制
- **預算上限：** 設定每日 API 調用限制
- **實時監控：** 即時成本追蹤
- **異常警報：** 成本超標自動停止

### 🛡️ 技術風險
- **容錯機制：** 完善的錯誤處理
- **數據備份：** 原始檔案與結果雙重備份
- **版本控制：** Git 管理所有變更

## 支援與維護

如果遇到問題，請檢查：

1. **API 金鑰設定**：確保 OpenAI API 金鑰正確
2. **檔案路徑**：確認 sample_500 資料夾存在
3. **相依套件**：安裝所有必要的 Python 套件
4. **錯誤日誌**：查看詳細錯誤訊息

## 總結

透過這個優化方案，您可以：
- 💰 **節省 80% 成本**：從 $150 降到 $30
- ⚡ **提升處理速度**：減少 67% API 調用
- 📊 **保持準確度**：智能兩階段提取策略
- 🔄 **向後相容**：隨時可切回原始版本

預計為您的 500 篇判決書分析專案節省約 **$120** 的成本！