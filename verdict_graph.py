"""
判決書分析系統
從 Jupyter notebook 轉換而來的 Python 檔案
"""

# 判決書分析
# Load Json file
# 不使用JsonLoader..我發現自己轉成Document還比較知道過程發生了什麼，也方便自己加東加西

import json
from pathlib import Path
from pprint import pprint

from langchain_core.documents import Document


def load_judgment_data(file_path: str):
    """載入判決書 JSON 檔案"""
    file_path = Path(file_path)
    
    with file_path.open(encoding="utf-8") as f:
        data = json.load(f)
    
    pprint(data)
    
    # 加入自訂
    jdate = data.get("JDATE")
    jyear = jdate[:4]   # 年
    jmonth = jdate[4:6] # 月
    jday = jdate[6:]    # 日

    content_block = {
        "content": data.get("JFULL", ""),
        "metadata": {
            "title": data.get("JTITLE"),
            "date": data.get("JDATE"),
            "id": data.get("JID"),
            "no": data.get("JNO"),
            "year": data.get("JYEAR"),
            "case": data.get("JCASE"),
            "pdf_url": data.get("JPDF"),
            "judgment_year": jyear,
            "judgment_month": jmonth,
            "judgment_day": jday,
            "judgment_date": f"{jyear}-{jmonth}-{jday}",
        }
    }

    content_text = json.dumps(content_block, ensure_ascii=False)
    
    # 建立Document
    doc = Document(page_content=content_text, metadata={})
    return doc


# 定義要萃取的分類schema
from typing import List, Optional

from pydantic import BaseModel, Field


class Prosecutor(BaseModel):
    name: Optional[str] = Field(None, description="檢察官姓名，若未具名可為 None")
    is_anonymous: Optional[bool] = Field(None, description="是否為匿名檢察官，匿名的特性是最後兩個字為○○")


class Judge(BaseModel):
    name: Optional[str] = Field(None, description="法官姓名，若未具名可為 None")
    is_presiding: Optional[bool] = Field(None, description="是否為審判長（主審法官）,如果本案只有一個法官，則為 True")
    is_anonymous: Optional[bool] = Field(None, description="是否為匿名法官，匿名的特性是姓名最後兩個字為○○")


class Clerk(BaseModel):
    name: Optional[str] = Field(None, description="書記官姓名，若未具名可為 None")
    is_anonymous: Optional[bool] = Field(None, description="是否為匿名書記官，匿名的特性是最後姓名兩個字為○○")


class Person(BaseModel):
    """
    只取得案件中的人物資料，不取得法官、檢察官、書記官、聲請人等職務資料
    """
    name: str = Field(..., description="人物姓名或代稱")
    uid: Optional[str] = Field(None, description="人物的唯一識別碼，若有")
    role: str = Field(..., description="人物在案件中的角色，請使用自然語言描述，如『被告』、『證人』、『證人母親』")
    gender: Optional[str] = Field(None, description="人物性別（若可推論）")
    is_anonymous: Optional[bool] = Field(None, description="是否為匿名人物，匿名的特性是姓名最後兩個字為○○")
    relationship_to_others: Optional[str] = Field(None, description="與他人的關係描述，例如：『主嫌女友』")


class Organization(BaseModel):
    """
    只取得案件中提到的公司或組織，不包含檢察署、法院等
    """
    name: str = Field(..., description="組織名稱，例如保險公司、法院、醫院")
    org_type: str = Field(..., description="組織類型，請使用自然語言描述，如『民營保險公司』、『醫療機構』")
    related_persons: Optional[List[str]] = Field(None, description="與該組織有關聯的人物姓名清單")


class Location(BaseModel):
    name: str = Field(..., description="地點名稱，例如『澎湖縣203縣道3.75公里』")
    type: Optional[str] = Field(None, description="地點類型，如事故現場、公司地址等")
    address: Optional[str] = Field(None, description="完整地址（如有）")


class Law(BaseModel):
    law_name: str = Field(..., description="完整法條名稱，例如『刑法第185條之3第1項』")
    article: Optional[str] = Field(None, description="條號簡寫，例如『185-3-1』")
    description: Optional[str] = Field(None, description="法條摘要或罪名，例如『酒駕致人於死』")


class EventDetails(BaseModel):
    amount: Optional[int] = Field(None, description="涉案金額，若有金流")
    bank: Optional[str] = Field(None, description="涉及銀行名稱")
    reason: Optional[str] = Field(None, description="付款理由或詐騙藉口")
    method: Optional[str] = Field(None, description="事件方式，例如『潑汽油引燃』")
    confession: Optional[str] = Field(None, description="證人或被告的供述內容")
    credibility: Optional[str] = Field(None, description="供述可信度（如 high / low / unknown）")
    evidence_metrics: Optional[str] = Field(None, description="自由文字描述的數值或檢測結果，例如『酒測值為0.34mg/L』")


class Event(BaseModel):
    event_type: str = Field(..., description="事件類型，請使用自然語言描述，如『酒後駕車』、『縱火準備行為』")
    event_id: Optional[str] = Field(None, description="事件的唯一識別碼，若有")
    participants: List[str] = Field(..., description="參與事件的人物姓名清單")
    target_persons: Optional[List[str]] = Field(None, description="受害人或目標對象的姓名清單")
    location: Optional[str] = Field(None, description="事件發生地點的名稱")
    date_time: Optional[str] = Field(None, description="事件發生時間，建議使用 yyyy-mm-dd 或 yyyy-mm-dd hh:mm 格式")
    details: Optional[EventDetails] = Field(None, description="事件的細節描述")


class PenaltyItem(BaseModel):
    type: str = Field(..., description="處罰類型，例如：罰金、拘役、沒收、追徵、緩刑、免訴等")
    content: str = Field(..., description=(
        "簡明描述該項處罰的重點資訊，例如金額或天數。"
        "避免使用冗長句子，只保留數字與關鍵詞。"
        "例如：『罰金8000元』、『拘役20日』、『沒收汽油100元』")
    )


class CaseInfo(BaseModel):
    case_id: str = Field(..., description="完整案件 ID，例如 'TCDM,111,訴,2522,20240124,2'")
    case_year: Optional[str] = Field(None, description="案件年度 (中華民國紀年)")
    judgment_year: Optional[str] = Field(None, description="判決年度 (格式yyyy)")
    judgment_month: Optional[str] = Field(None, description="判決月份 (格式mm)")
    judgment_day: Optional[str] = Field(None, description="判決日期 (格式dd)")
    case_type: str = Field(..., description="案件類型代碼，如『訴』、『交訴』")
    case_number: Optional[str] = Field(None, description="案件流水號")
    title: Optional[str] = Field(None, description="案件標題，例如『違反商業會計法等』")
    judgment_type: str = Field(..., description="判決類型（如 substantive, procedural, other）")
    verdict_items: Optional[List[PenaltyItem]] = Field(None, description="主文中所有處罰項目的清單，每項包含類型與簡述，例如 [{'type': '罰金', 'content': '處罰金新臺幣8000元'}]")
    judgment_date: Optional[str] = Field(None, description="判決日期（格式 yyyy-mm-dd）")
    court: Optional[str] = Field(None, description="審理法院名稱")
    pdf_url: Optional[str] = Field(None, description="原始判決書 PDF 下載連結")
    prosecutors: Optional[List[Prosecutor]] = Field(None, description="參與本案的檢察官清單")
    judges: Optional[List[Judge]] = Field(None, description="參與本案的法官清單")
    clerks: Optional[Clerk] = Field(None, description="參與本案的書記官")


class Classification(BaseModel):
    case: CaseInfo = Field(..., description="案件的基本資料")
    people: List[Person] = Field(..., description="案件中的所有人物")
    organizations: Optional[List[Organization]] = Field(None, description="組織或公司相關資訊")
    locations: Optional[List[Location]] = Field(None, description="判決中提及的地點資訊")
    laws: Optional[List[Law]] = Field(None, description="所引用或涉及的法條")
    events: Optional[List[Event]] = Field(None, description="與本案有關的事件敘述與參與人")


# 初始化LLM
import getpass
import os


def setup_llm(model="gpt-4o-mini"):
    """設定 OpenAI LLM，預設使用 gpt-4o-mini 節省成本"""
    if not os.environ.get("OPENAI_API_KEY"):
        os.environ["OPENAI_API_KEY"] = getpass.getpass("Enter API key for OpenAI: ")
    
    from langchain_openai import ChatOpenAI
    return ChatOpenAI(temperature=0, model=model)


# 建立Prompt templates
from langchain_core.prompts import ChatPromptTemplate

# ============ 兩階段合併調用的 Prompt 模板 ============

phase1_prompt_template = """
從以下判決書內容中，提取第一階段的基礎資訊，包含：

1. **案件基本資料**：案件ID、年度、類型、標題、判決日期、法院、主文等
2. **所有人物資訊**：被告、法官、檢察官、書記官、證人、被害人等（包含角色、姓名、匿名狀況等）
3. **組織機構資訊**：涉及的公司、機關、醫院、學校等組織

請嚴格按照提供的資料結構回傳，若找不到資料則回傳空值，不要發明或猜測內容。

判決書內容：
{content}
"""

phase2_prompt_template = """
基於以下判決書內容和已提取的人物資訊，提取第二階段的關聯資料：

1. **地點資訊**：案發地點、相關地址、具體位置等
2. **法條資訊**：引用的法條、條號、法條描述、適用罪名等
3. **事件資訊**：具體事件、參與人員、時間、地點、詳細情況等

**已知人物清單**：{people_context}

請將事件中的參與者和目標對象使用已知人物的姓名來關聯。
嚴格按照資料結構回傳，若找不到資料則回傳空值。

判決書內容：
{content}
"""

# 原始 prompt 模板（保留向後相容）
basic_prompt_template = """
從以下判決書內容中，僅提取 {fields} 相關的資訊，並依照結構化定義回傳。
若找不到資料，請回傳空陣列或空值，不要發明或猜測。

判決書內容：
{content}
"""

# 統一產生prompt
def build_prompt(fields: str, content: str):
    return ChatPromptTemplate.from_template(basic_prompt_template).invoke({
        "fields": fields,
        "content": content,
    })

def build_phase1_prompt(content: str):
    """構建第一階段提取的 prompt"""
    return ChatPromptTemplate.from_template(phase1_prompt_template).invoke({
        "content": content,
    })

def build_phase2_prompt(content: str, people_context: List[str]):
    """構建第二階段提取的 prompt"""
    people_list = "、".join(people_context) if people_context else "無"
    return ChatPromptTemplate.from_template(phase2_prompt_template).invoke({
        "content": content,
        "people_context": people_list,
    })


import sys
from collections import defaultdict
# 定義Node
from typing import Dict, List


# ============ 兩階段合併調用的 Schema ============

# 第一階段：基礎實體提取
class Phase1Extract(BaseModel):
    """第一階段提取：案件基本資料 + 人物 + 組織"""
    case: CaseInfo = Field(..., description="案件基本資料")
    people: List[Person] = Field(..., description="案件中的所有人物（包含法官、檢察官、書記官）")
    organizations: Optional[List[Organization]] = Field(None, description="組織或公司相關資訊")


# 第二階段：關聯資料提取
class Phase2Extract(BaseModel):
    """第二階段提取：地點 + 法條 + 事件"""
    locations: Optional[List[Location]] = Field(None, description="判決中提及的地點資訊")
    laws: Optional[List[Law]] = Field(None, description="所引用或涉及的法條")
    events: Optional[List[Event]] = Field(None, description="與本案有關的事件敘述與參與人")


# ============ 原始包裝 Schema（保留向後相容）============
class PeopleWrapper(BaseModel):
    people: List[Person] = Field(..., description="案件中的所有人物")


class OrgsWrapper(BaseModel):
    organizations: Optional[List[Organization]] = Field(None, description="組織或公司相關資訊")


class LocsWrapper(BaseModel):
    locations: Optional[List[Location]] = Field(None, description="判決中提及的地點資訊")


class LawsWrapper(BaseModel):
    laws: Optional[List[Law]] = Field(None, description="所引用或涉及的法條")


class EventsWrapper(BaseModel):
    events: Optional[List[Event]] = Field(None, description="與本案有關的事件敘述與參與人")


# 安全的提取欄資料位，給有Wapper的Schema拆解用
def safe_extract_field(result, field_name: str):
    if isinstance(result, tuple) and result[0] == field_name:
        return result[1]
    elif isinstance(result, BaseModel) and hasattr(result, field_name):
        return getattr(result, field_name)
    else:
        return []


# ============ 兩階段合併提取函數 ============

# 第一階段：基礎實體提取
async def extract_phase1(state: Dict):
    """第一階段提取：案件基本資料 + 人物 + 組織"""
    llm = state.get("llm")
    prompt = build_phase1_prompt(state["content"])
    
    try:
        result = await llm.with_structured_output(Phase1Extract).ainvoke(prompt)
        
        # 提取結果
        case_data = result.case.model_dump()
        people_data = [p.model_dump() for p in result.people]
        organizations_data = [o.model_dump() for o in result.organizations or []]
        
        return {
            "case": case_data,
            "case_id": case_data.get("case_id"),
            "people": people_data,
            "organizations": organizations_data,
            "phase1_complete": True
        }
    except Exception as e:
        print(f"第一階段提取錯誤: {e}")
        # 錯誤處理：返回空結果
        return {
            "case": {},
            "case_id": "",
            "people": [],
            "organizations": [],
            "phase1_complete": False,
            "error": str(e)
        }


# 第二階段：關聯資料提取
async def extract_phase2(state: Dict):
    """第二階段提取：地點 + 法條 + 事件"""
    llm = state.get("llm")
    
    # 獲取已提取的人物資訊作為上下文
    people_context = [p.get("name", "") for p in state.get("people", []) if p.get("name")]
    prompt = build_phase2_prompt(state["content"], people_context)
    
    try:
        result = await llm.with_structured_output(Phase2Extract).ainvoke(prompt)
        
        # 提取結果
        locations_data = [l.model_dump() for l in result.locations or []]
        laws_data = [l.model_dump() for l in result.laws or []]
        events_data = [e.model_dump() for e in result.events or []]
        
        return {
            "locations": locations_data,
            "laws": laws_data,
            "events": events_data,
            "phase2_complete": True
        }
    except Exception as e:
        print(f"第二階段提取錯誤: {e}")
        # 錯誤處理：返回空結果
        return {
            "locations": [],
            "laws": [],
            "events": [],
            "phase2_complete": False,
            "error": str(e)
        }


# ============ 原始單獨提取函數（保留向後相容）============

# 取得案件基本資料
async def extract_case_info(state: Dict):
    llm = state.get("llm")
    prompt = build_prompt("case資訊 (CaseInfo)", state["content"])
    result = await llm.with_structured_output(CaseInfo).ainvoke(prompt)
    return {"case": result.model_dump(), "case_id": result.case_id}


# 取得人物資料
async def extract_people(state: Dict):
    llm = state.get("llm")
    prompt = build_prompt("人物清單 (People)", state["content"])
    result = await llm.with_structured_output(PeopleWrapper).ainvoke(prompt)
    people = safe_extract_field(result, "people")
    return {"people": [p.model_dump() for p in people]}


# 取得組織清單
async def extract_organizations(state: Dict):
    llm = state.get("llm")
    prompt = build_prompt("組織清單 (Organizations)", state["content"])
    result = await llm.with_structured_output(OrgsWrapper).ainvoke(prompt)
    organizations = safe_extract_field(result, "organizations")
    return {"organizations": [o.model_dump() for o in organizations]}


# 取得地點清單
async def extract_locations(state: Dict):
    llm = state.get("llm")
    prompt = build_prompt("地點清單 (Locations)", state["content"])
    result = await llm.with_structured_output(LocsWrapper).ainvoke(prompt)
    locations = safe_extract_field(result, "locations")
    return {"locations": [l.model_dump() for l in locations]}


# 取得法條清單
async def extract_laws(state: Dict):
    llm = state.get("llm")
    prompt = build_prompt("法條清單 (Laws)", state["content"])
    result = await llm.with_structured_output(LawsWrapper).ainvoke(prompt)
    laws = safe_extract_field(result, "laws")
    return {"laws": [l.model_dump() for l in laws]}


# 取得事件清單
async def extract_events(state: Dict):
    llm = state.get("llm")
    prompt = build_prompt("事件清單 (Events)", state["content"])
    result = await llm.with_structured_output(EventsWrapper).ainvoke(prompt)
    events = safe_extract_field(result, "events")
    return {"events": [e.model_dump() for e in events]}


# 整合所有結果  
async def merge_results(state: Dict):
    case = state["case"]
    case_id = case["case_id"]

    # --------- 統一補上 role / uid 給特殊角色（檢察官、法官、書記官）---------
    role_defaults = {
        "prosecutors": "檢察官",
        "judges": "法官",
        "clerks": "書記官"
    }

    special_people = []

    for key, default_role in role_defaults.items():
        value = case.get(key)
        if isinstance(value, list):
            for idx, item in enumerate(value, 1):
                person = dict(item)
                role = person.get("role", default_role)
                person["role"] = role
                special_people.append(person)
        elif isinstance(value, dict):  # e.g. clerks
            person = dict(value)
            role = person.get("role", default_role)
            person["role"] = role
            special_people.append(person)

    # --------- 合併所有人物並補上 uid ---------
    people = state.get("people", [])
    people = [dict(p) for p in people]  # 確保是可變 dict
    all_people = people + special_people
    role_counter = defaultdict(int)
    
    for person in all_people:
        # 修正可能的角色名稱錯字
        role = person["role"].replace(" ", "")
        person["role"] = role  # 更新角色名稱
        role_counter[role] += 1
        person["uid"] = f"{case_id}_{role}_{role_counter[role]}"

    # --------- 移除 case 中的特殊角色欄位，避免重複 ---------
    case.pop("prosecutors", None)
    case.pop("judges", None)
    case.pop("clerks", None)

    # --------- 建立 name → uid 對應表（只限非匿名） ---------
    name_to_uid = {
        p["name"]: p["uid"]
        for p in all_people
        if p.get("name") and not p.get("is_anonymous", False)
    }

    # --------- 補上 event_id，並將 name 轉為 uid ---------
    events = state.get("events", [])
    events = [dict(ev) for ev in events]  # 確保是可變 dict
    for idx, ev in enumerate(events, 1):
        if not ev.get("event_id"):
            ev["event_id"] = f"{case_id}_ev{idx}"

        # 安全處理 participants 和 target_persons，避免 None 值
        participants = ev.get("participants") or []
        target_persons = ev.get("target_persons") or []
        
        ev["participants"] = [
            name_to_uid.get(name, name) for name in participants if name
        ]
        ev["target_persons"] = [
            name_to_uid.get(name, name) for name in target_persons if name
        ]

    # --------- 組織裡的 related_persons 也轉成 uid ---------
    organizations = state.get("organizations", [])
    organizations = [dict(o) for o in organizations]
    for org in organizations:
        org["related_persons"] = [
            name_to_uid.get(name, name)
            for name in org.get("related_persons", []) or []
        ]

    # --------- verdict_items 轉為 PenaltyItem 型別 ---------
    verdict_items = case.get("verdict_items")
    if verdict_items and isinstance(verdict_items, list) and all(isinstance(v, str) for v in verdict_items):
        case["verdict_items"] = [
            PenaltyItem(type=v.split(":")[0].strip(), content=v.split(":", 1)[1].strip())
            for v in verdict_items if ":" in v
        ]

    # --------- 整合成 Classification ---------
    merged = {
        "case": case,
        "people": all_people,
        "organizations": organizations,
        "locations": state.get("locations", []),
        "laws": state.get("laws", []),
        "events": events,
    }

    return {"classification": Classification(**merged)}


# Compile graph
from typing import List, TypedDict


class JudgmentState(TypedDict, total=False):
    content: str
    case: dict
    people: List[dict]
    organizations: List[dict]
    locations: List[dict]
    laws: List[dict]
    events: List[dict]
    classification: dict
    llm: object


def create_workflow(use_optimized=True):
    """建立工作流程
    
    Args:
        use_optimized (bool): True 使用兩階段優化版本，False 使用原始 6 階段版本
    """
    from langgraph.graph import END, StateGraph
    
    graph = StateGraph(JudgmentState)
    
    if use_optimized:
        # 兩階段優化工作流程 - 節省 67% API 調用次數
        graph.add_node("extract_phase1", extract_phase1)
        graph.add_node("extract_phase2", extract_phase2)
        graph.add_node("merge_results", merge_results)
        
        graph.set_entry_point("extract_phase1")
        graph.add_edge("extract_phase1", "extract_phase2")
        graph.add_edge("extract_phase2", "merge_results")
        graph.add_edge("merge_results", END)
    else:
        # 原始 6 階段工作流程（向後相容）
        graph.add_node("extract_case_info", extract_case_info)
        graph.add_node("extract_people", extract_people)
        graph.add_node("extract_organizations", extract_organizations)
        graph.add_node("extract_locations", extract_locations)
        graph.add_node("extract_laws", extract_laws)
        graph.add_node("extract_events", extract_events)
        graph.add_node("merge_results", merge_results)
        
        graph.set_entry_point("extract_case_info")
        graph.add_edge("extract_case_info", "extract_people")
        graph.add_edge("extract_people", "extract_organizations")
        graph.add_edge("extract_organizations", "extract_locations")
        graph.add_edge("extract_locations", "extract_laws")
        graph.add_edge("extract_laws", "extract_events")
        graph.add_edge("extract_events", "merge_results")
        graph.add_edge("merge_results", END)
    
    return graph.compile()


def create_optimized_workflow():
    """建立兩階段優化工作流程（節省 67% API 調用）"""
    return create_workflow(use_optimized=True)


def create_legacy_workflow():
    """建立原始 6 階段工作流程（向後相容）"""
    return create_workflow(use_optimized=False)


# Classification轉換成Cypher
def classification_to_cypher(classification: Classification) -> str:
    """
    將 Classification 物件轉換成 Neo4j Cypher 查詢
    使用分離式語句避免變數作用域問題
    """
    queries = []
    case = classification.case
    case_id = case.case_id

    # --- Case Node ---
    verdict_items = [f"{v.type}: {v.content}" for v in case.verdict_items or []]
    queries.append(f"MERGE (c:Case {{case_id: '{case_id}'}});")
    queries.append(f"MATCH (c:Case {{case_id: '{case_id}'}}) SET c += {{" + ", ".join([
        f"case_year: '{case.case_year}'",
        f"judgment_year: '{case.judgment_year}'",
        f"judgment_month: '{case.judgment_month}'",
        f"judgment_day: '{case.judgment_day}'",
        f"case_type: '{case.case_type}'",
        f"case_number: '{case.case_number}'",
        f"title: '{case.title}'",
        f"judgment_type: '{case.judgment_type}'",
        f"judgment_date: '{case.judgment_date}'",
        f"court: '{case.court}'",
        f"pdf_url: '{case.pdf_url}'",
        f"verdict_items: {verdict_items}"
    ]) + "};")

    # --- People Nodes ---
    if classification.people:
        for idx, p in enumerate(classification.people):
            queries.append(f"MERGE (p{idx}:Person {{uid: '{p.uid}'}});")
            queries.append(f"MATCH (p:Person {{uid: '{p.uid}'}}) SET p += {{" + ", ".join([
                f"name: '{p.name}'",
                f"role: '{p.role}'",
                f"gender: '{p.gender or ''}'",
                f"relationship_to_others: '{p.relationship_to_others or ''}'",
                f"is_anonymous: {p.is_anonymous}"
            ]) + "};")

    # --- Organization Nodes ---
    if classification.organizations:
        for idx, o in enumerate(classification.organizations):
            queries.append(f"MERGE (o{idx}:Organization {{name: '{o.name}'}});")
            queries.append(f"MATCH (o:Organization {{name: '{o.name}'}}) SET o.org_type = '{o.org_type}';")
            
            # 建立組織關係
            if o.related_persons:
                for pid in o.related_persons:
                    queries.append(f"MATCH (o:Organization {{name: '{o.name}'}}) MATCH (p:Person {{uid: '{pid}'}}) MERGE (p)-[:BELONGS_TO]->(o);")

    # --- Location Nodes ---
    if classification.locations:
        for idx, l in enumerate(classification.locations):
            queries.append(f"MERGE (l{idx}:Location {{name: '{l.name}'}});")
            queries.append(f"MATCH (l:Location {{name: '{l.name}'}}) SET l.type = '{l.type or ''}', l.address = '{l.address or ''}';")

    # --- Law Nodes and Relations ---
    if classification.laws:
        for idx, law in enumerate(classification.laws):
            law_query = [
                f"MATCH (c:Case {{case_id: '{case_id}'}})",
                f"MERGE (law{idx}:Law {{article: '{law.article}'}})",
                f"SET law{idx}.law_name = '{law.law_name}', law{idx}.description = '{law.description}'",
                f"MERGE (c)-[:CITES_LAW]->(law{idx});"
            ]
            queries.append("\n".join(law_query))

    # --- Event Nodes and Relations ---
    if classification.events:
        for idx, e in enumerate(classification.events):
            # 創建事件節點
            event_query = [
                f"MATCH (c:Case {{case_id: '{case_id}'}})",
                f"MERGE (e{idx}:Event {{event_id: '{e.event_id}'}})",
                f"SET e{idx}.event_type = '{e.event_type}', e{idx}.date_time = '{e.date_time}'"
            ]
            
            if e.details:
                d = e.details.model_dump()
                for k, v in d.items():
                    if v is not None:
                        v_str = f"'{v}'" if isinstance(v, str) else v
                        event_query.append(f"SET e{idx}.{k} = {v_str}")
            
            event_query.append(f"MERGE (c)-[:HAS_EVENT]->(e{idx});")
            queries.append("\n".join(event_query))
            
            # 連接參與者
            if e.participants:
                for pid in e.participants:
                    participant_query = [
                        f"MATCH (e:Event {{event_id: '{e.event_id}'}})",
                        f"MATCH (p:Person {{uid: '{pid}'}})",
                        f"MERGE (e)-[:HAS_PARTICIPANT]->(p);"
                    ]
                    queries.append("\n".join(participant_query))
            
            # 連接受害者
            if e.target_persons:
                for tid in e.target_persons:
                    target_query = [
                        f"MATCH (e:Event {{event_id: '{e.event_id}'}})",
                        f"MATCH (p:Person {{uid: '{tid}'}})",
                        f"MERGE (e)-[:TARGET]->(p);"
                    ]
                    queries.append("\n".join(target_query))
            
            # 連接地點
            if e.location:
                location_query = [
                    f"MATCH (e:Event {{event_id: '{e.event_id}'}})",
                    f"MATCH (l:Location {{name: '{e.location}'}})",
                    f"MERGE (e)-[:IN_LOCATION]->(l);"
                ]
                queries.append("\n".join(location_query))

    return "\n\n".join(queries)


# ============ 成本追蹤功能 ============
import time
from typing import Optional

class CostTracker:
    """API 成本追蹤器"""
    def __init__(self):
        self.reset()
    
    def reset(self):
        self.total_calls = 0
        self.start_time = time.time()
        self.calls_log = []
    
    def log_call(self, model: str, input_tokens: int = 0, output_tokens: int = 0):
        self.total_calls += 1
        # GPT-4o-mini 價格：$0.00015/1K input, $0.0006/1K output
        # GPT-4o 價格：$0.0025/1K input, $0.01/1K output
        if "gpt-4o-mini" in model.lower():
            cost = (input_tokens * 0.00015 / 1000) + (output_tokens * 0.0006 / 1000)
        elif "gpt-4o" in model.lower():
            cost = (input_tokens * 0.0025 / 1000) + (output_tokens * 0.01 / 1000)
        else:
            cost = 0
        
        self.calls_log.append({
            "model": model,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "cost": cost,
            "timestamp": time.time()
        })
    
    def get_summary(self):
        total_cost = sum(call["cost"] for call in self.calls_log)
        total_time = time.time() - self.start_time
        return {
            "total_calls": self.total_calls,
            "total_cost": round(total_cost, 4),
            "total_time": round(total_time, 2),
            "avg_cost_per_call": round(total_cost / max(self.total_calls, 1), 4)
        }

# 全局成本追蹤器
cost_tracker = CostTracker()


# ============ 主要執行函數 ============

async def process_judgment(file_path: str, use_optimized=True, model="gpt-4o-mini"):
    """處理判決書的主要函數
    
    Args:
        file_path (str): 判決書檔案路徑
        use_optimized (bool): 是否使用兩階段優化版本
        model (str): 使用的 LLM 模型
    """
    print(f"開始處理: {file_path}")
    print(f"使用模型: {model}")
    print(f"使用{'兩階段優化' if use_optimized else '原始6階段'}工作流程")
    
    # 載入判決書資料
    doc = load_judgment_data(file_path)
    
    # 設定 LLM
    llm = setup_llm(model)
    
    # 建立工作流程
    workflow = create_workflow(use_optimized)
    
    # 重設成本追蹤
    cost_tracker.reset()
    
    try:
        # 執行分析
        result = await workflow.ainvoke({
            "content": doc.page_content,
            "llm": llm
        })
        
        # 顯示成本統計
        cost_summary = cost_tracker.get_summary()
        print("\n=== 成本統計 ===")
        print(f"API 調用次數: {cost_summary['total_calls']}")
        print(f"處理時間: {cost_summary['total_time']} 秒")
        print(f"預估成本: ${cost_summary['total_cost']} USD")
        print(f"平均每次調用成本: ${cost_summary['avg_cost_per_call']} USD")
        
        print("\n=== 分析結果 ===")
        classification = result["classification"]
        print(f"案件ID: {classification.case.case_id}")
        print(f"人物數量: {len(classification.people)}")
        print(f"事件數量: {len(classification.events or [])}")
        
        return classification
        
    except Exception as e:
        print(f"處理錯誤: {e}")
        return None


async def process_judgment_optimized(file_path: str):
    """使用兩階段優化版本處理判決書（快捷方式）"""
    return await process_judgment(file_path, use_optimized=True, model="gpt-4o-mini")


async def process_judgment_legacy(file_path: str):
    """使用原始6階段版本處理判決書（快捷方式）"""
    return await process_judgment(file_path, use_optimized=False, model="gpt-4o")


async def batch_process_judgments(file_paths: list, use_optimized=True, model="gpt-4o-mini"):
    """批量處理判決書"""
    results = []
    total_start_time = time.time()
    
    print(f"開始批量處理 {len(file_paths)} 個判決書檔案")
    print(f"使用模型: {model}")
    print(f"使用{'兩階段優化' if use_optimized else '原始6階段'}工作流程")
    
    for i, file_path in enumerate(file_paths, 1):
        print(f"\n處理進度: {i}/{len(file_paths)}")
        result = await process_judgment(file_path, use_optimized, model)
        results.append(result)
    
    total_time = time.time() - total_start_time
    print(f"\n=== 批量處理完成 ===")
    print(f"總處理時間: {total_time:.2f} 秒")
    print(f"平均每個檔案: {total_time/len(file_paths):.2f} 秒")
    
    return results


# ============ 測試和比較功能 ============

async def test_cost_comparison(test_files: list = None, num_samples: int = 5):
    """測試成本比較：原始版本 vs 優化版本"""
    from pathlib import Path
    
    if not test_files:
        sample_dir = Path("sample_500")
        all_files = list(sample_dir.glob("*.json"))[:num_samples]
        test_files = [str(f) for f in all_files]
    
    print("=== 成本比較測試 ===")
    print(f"測試檔案數量: {len(test_files)}")
    
    # 測試原始版本 (GPT-4o + 6階段)
    print("\n--- 測試原始版本 (GPT-4o + 6階段) ---")
    legacy_start = time.time()
    cost_tracker.reset()
    
    for file_path in test_files:
        await process_judgment(file_path, use_optimized=False, model="gpt-4o")
    
    legacy_summary = cost_tracker.get_summary()
    legacy_time = time.time() - legacy_start
    
    # 測試優化版本 (GPT-4o-mini + 2階段)
    print("\n--- 測試優化版本 (GPT-4o-mini + 2階段) ---")
    optimized_start = time.time()
    cost_tracker.reset()
    
    for file_path in test_files:
        await process_judgment(file_path, use_optimized=True, model="gpt-4o-mini")
    
    optimized_summary = cost_tracker.get_summary()
    optimized_time = time.time() - optimized_start
    
    # 計算節省比例
    cost_savings = (legacy_summary["total_cost"] - optimized_summary["total_cost"]) / legacy_summary["total_cost"] * 100
    time_savings = (legacy_time - optimized_time) / legacy_time * 100
    call_reduction = (legacy_summary["total_calls"] - optimized_summary["total_calls"]) / legacy_summary["total_calls"] * 100
    
    print("\n=== 比較結果 ===")
    print(f"原始版本總成本: ${legacy_summary['total_cost']:.4f}")
    print(f"優化版本總成本: ${optimized_summary['total_cost']:.4f}")
    print(f"成本節省: {cost_savings:.1f}%")
    print(f"")
    print(f"原始版本 API 調用: {legacy_summary['total_calls']}")
    print(f"優化版本 API 調用: {optimized_summary['total_calls']}")
    print(f"調用次數減少: {call_reduction:.1f}%")
    print(f"")
    print(f"原始版本處理時間: {legacy_time:.2f} 秒")
    print(f"優化版本處理時間: {optimized_time:.2f} 秒")
    print(f"時間節省: {time_savings:.1f}%")
    
    # 推算 500 個檔案的成本
    legacy_500_cost = legacy_summary["total_cost"] * (500 / len(test_files))
    optimized_500_cost = optimized_summary["total_cost"] * (500 / len(test_files))
    total_savings = legacy_500_cost - optimized_500_cost
    
    print(f"\n=== 500個檔案推算 ===")
    print(f"原始版本預估成本: ${legacy_500_cost:.2f}")
    print(f"優化版本預估成本: ${optimized_500_cost:.2f}")
    print(f"總共可節省: ${total_savings:.2f}")


if __name__ == "__main__":
    import asyncio
    from pathlib import Path

    # 使用範例
    sample_dir = Path("sample_500")
    json_files = list(sample_dir.glob("*.json"))
    
    print("判決書分析系統 - 成本優化版本")
    print("=" * 50)
    print("可用的執行模式:")
    print("1. 測試成本比較 (推薦)")
    print("2. 批量處理 - 優化版本")
    print("3. 批量處理 - 原始版本")
    print("4. 單個檔案測試")
    
    # 預設執行測試比較
    mode = input("\n請選擇執行模式 (1-4，預設1): ").strip() or "1"
    
    if mode == "1":
        # 成本比較測試
        print("\n執行成本比較測試...")
        asyncio.run(test_cost_comparison(num_samples=3))
        
    elif mode == "2":
        # 批量處理 - 優化版本
        print(f"\n使用優化版本批量處理 {len(json_files)} 個檔案...")
        results = asyncio.run(batch_process_judgments(
            [str(f) for f in json_files[:10]],
            use_optimized=True,
            model="gpt-4o-mini"
        ))
        
    elif mode == "3":
        # 批量處理 - 原始版本
        print(f"\n使用原始版本批量處理 {len(json_files)} 個檔案...")
        results = asyncio.run(batch_process_judgments(
            [str(f) for f in json_files[:5]],
            use_optimized=False,
            model="gpt-4o"
        ))
        
    elif mode == "4":
        # 單個檔案測試
        if json_files:
            test_file = str(json_files[0])
            print(f"\n測試單個檔案: {test_file}")
            result = asyncio.run(process_judgment_optimized(test_file))
            if result:
                cypher_query = classification_to_cypher(result)
                print("\nCypher 查詢:")
                print(cypher_query[:500] + "..." if len(cypher_query) > 500 else cypher_query)
    
    print("\n執行完成！")