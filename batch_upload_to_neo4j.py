"""
大量上傳判決書到 Neo4j
"""
import asyncio
import json
import os
from pathlib import Path

PROGRESS_FILE = Path("progress.json")

from dotenv import load_dotenv
from neo4j import GraphDatabase

from verdict_graph import classification_to_cypher, process_judgment

# 讀入 Neo4j 資訊
load_dotenv()
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD")

# 路徑設定
SAMPLE_DIR = Path("sample_500")
OUTPUT_DIR = Path("cypher_output")
OUTPUT_DIR.mkdir(exist_ok=True)

# 建立 Neo4j 連線
driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))

def run_cypher_in_neo4j(cypher_query: str):
    with driver.session() as session:
        session.run(cypher_query)

async def process_and_upload(file_path: Path):
    try:
        print(f"📄 處理: {file_path.name}")

        # 檢查是否已經處理過
        output_path = OUTPUT_DIR / (file_path.stem + ".cypher")
        if output_path.exists():
            print(f"⏭️  已存在，跳過: {output_path.name}")
            return

        result = await process_judgment(str(file_path))
        cypher_query = classification_to_cypher(result)

        # 先備份 .cypher（即使 Neo4j 寫入失敗也能保存）
        output_path.write_text(cypher_query, encoding="utf-8")
        print(f"💾 備份：{output_path.name}")

        # 寫入 Neo4j
        try:
            run_cypher_in_neo4j(cypher_query)
            print("✅ 已寫入 Neo4j")
        except Exception as neo4j_error:
            print(f"⚠️  Neo4j 寫入失敗: {neo4j_error}")
            print("💾 Cypher 已備份，可稍後手動執行")

    except Exception as e:
        # 檢查是否為 API 配額問題
        if "insufficient_quota" in str(e) or "429" in str(e):
            print(f"💰 API 配額不足: {file_path.name}")
            print("請檢查 OpenAI 帳戶配額或稍後重試")
            return False  # 返回 False 表示應該停止處理
        else:
            print(f"❌ 失敗: {file_path.name} - {e}")

    return True  # 返回 True 表示可以繼續處理

async def main():
    files = list(SAMPLE_DIR.glob("*.json"))
    print(f"🔍 找到 {len(files)} 個檔案")

    # 讀取進度檔
    if PROGRESS_FILE.exists():
        with PROGRESS_FILE.open("r", encoding="utf-8") as f:
            finished = set(json.load(f))
    else:
        finished = set()

    processed = 0
    skipped = 0
    failed = 0

    for i, file_path in enumerate(files, 1):
        print(f"\n[{i}/{len(files)}] ", end="")

        if file_path.name in finished:
            print(f"⏭️  已記錄完成，跳過: {file_path.name}")
            skipped += 1
            continue

        result = await process_and_upload(file_path)

        if result is False:  # API 配額不足
            print(f"\n⏸️  因 API 配額不足停止處理，已處理 {processed} 個檔案")
            break
        elif result is True:
            processed += 1
            finished.add(file_path.name)
            # 即時更新進度檔
            with PROGRESS_FILE.open("w", encoding="utf-8") as f:
                json.dump(list(finished), f, ensure_ascii=False, indent=2)
        else:
            failed += 1

    print(f"\n📊 處理完成:")
    print(f"   ✅ 成功: {processed}")
    print(f"   ⏭️  跳過: {skipped}")
    print(f"   ❌ 失敗: {failed}")

    driver.close()

if __name__ == "__main__":
    asyncio.run(main())
