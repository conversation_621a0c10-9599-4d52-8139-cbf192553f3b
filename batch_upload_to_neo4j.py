"""
大量上傳判決書到 Neo4j - 支援優化版本
"""
import asyncio
import json
import os
import time
from pathlib import Path

PROGRESS_FILE = Path("progress.json")
COST_LOG_FILE = Path("cost_log.json")

from dotenv import load_dotenv
from neo4j import GraphDatabase

from verdict_graph import (classification_to_cypher, cost_tracker,
                           process_judgment)

# 讀入 Neo4j 資訊
load_dotenv()
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD")

# 路徑設定
SAMPLE_DIR = Path("sample_500")
OUTPUT_DIR = Path("cypher_output")
OUTPUT_DIR.mkdir(exist_ok=True)

# 建立 Neo4j 連線
driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))

def run_cypher_in_neo4j(cypher_query: str):
    """執行 Cypher 查詢，支援多個語句"""
    with driver.session() as session:
        # 分割多個語句並逐一執行
        statements = [stmt.strip() for stmt in cypher_query.split(';') if stmt.strip()]
        for statement in statements:
            if statement:  # 確保語句不為空
                session.run(statement)

async def process_and_upload(file_path: Path, use_optimized=True, model="gpt-4o-mini"):
    """
    處理單個判決書檔案並上傳到 Neo4j

    Args:
        file_path: 判決書檔案路徑
        use_optimized: 是否使用優化版本（兩階段提取）
        model: 使用的 LLM 模型
    """
    try:
        workflow_type = "優化版本" if use_optimized else "原始版本"
        print(f"📄 處理: {file_path.name} ({workflow_type}, {model})")

        # 檢查是否已經處理過
        output_path = OUTPUT_DIR / (file_path.stem + ".cypher")
        if output_path.exists():
            print(f"⏭️  已存在，跳過: {output_path.name}")
            return True

        # 記錄處理開始時間
        start_time = time.time()

        # 重設成本追蹤器
        cost_tracker.reset()

        # 使用指定的版本和模型處理判決書
        result = await process_judgment(str(file_path), use_optimized=use_optimized, model=model)

        if result is None:
            print(f"❌ 處理失敗: {file_path.name}")
            return False

        cypher_query = classification_to_cypher(result)

        # 先備份 .cypher（即使 Neo4j 寫入失敗也能保存）
        output_path.write_text(cypher_query, encoding="utf-8")

        # 記錄成本資訊
        cost_summary = cost_tracker.get_summary()
        processing_time = time.time() - start_time

        print(f"💾 備份：{output_path.name}")
        print(f"⏱️  處理時間: {processing_time:.2f}秒, API調用: {cost_summary['total_calls']}次, 成本: ${cost_summary['total_cost']:.4f}")

        # 寫入 Neo4j
        try:
            run_cypher_in_neo4j(cypher_query)
            print("✅ 已寫入 Neo4j")
        except Exception as neo4j_error:
            print(f"⚠️  Neo4j 寫入失敗: {neo4j_error}")
            print("💾 Cypher 已備份，可稍後手動執行")

        # 記錄成本日誌
        log_cost_info(file_path.name, cost_summary, processing_time, use_optimized, model)

        return True

    except Exception as e:
        # 檢查是否為 API 配額問題
        if "insufficient_quota" in str(e) or "429" in str(e):
            print(f"💰 API 配額不足: {file_path.name}")
            print("請檢查 OpenAI 帳戶配額或稍後重試")
            return False  # 返回 False 表示應該停止處理
        else:
            print(f"❌ 失敗: {file_path.name} - {e}")
            return False

def log_cost_info(filename: str, cost_summary: dict, processing_time: float, use_optimized: bool, model: str):
    """記錄成本資訊到日誌檔案"""
    cost_entry = {
        "filename": filename,
        "timestamp": time.time(),
        "processing_time": processing_time,
        "use_optimized": use_optimized,
        "model": model,
        "api_calls": cost_summary["total_calls"],
        "total_cost": cost_summary["total_cost"],
        "avg_cost_per_call": cost_summary["avg_cost_per_call"]
    }

    # 讀取現有日誌
    if COST_LOG_FILE.exists():
        with COST_LOG_FILE.open("r", encoding="utf-8") as f:
            cost_log = json.load(f)
    else:
        cost_log = []

    # 添加新記錄
    cost_log.append(cost_entry)

    # 寫回檔案
    with COST_LOG_FILE.open("w", encoding="utf-8") as f:
        json.dump(cost_log, f, ensure_ascii=False, indent=2)

def print_cost_summary():
    """顯示成本統計摘要"""
    if not COST_LOG_FILE.exists():
        print("📊 尚無成本記錄")
        return

    with COST_LOG_FILE.open("r", encoding="utf-8") as f:
        cost_log = json.load(f)

    if not cost_log:
        print("📊 尚無成本記錄")
        return

    total_cost = sum(entry["total_cost"] for entry in cost_log)
    total_calls = sum(entry["api_calls"] for entry in cost_log)
    total_time = sum(entry["processing_time"] for entry in cost_log)

    optimized_entries = [e for e in cost_log if e["use_optimized"]]
    legacy_entries = [e for e in cost_log if not e["use_optimized"]]

    print(f"\n📊 成本統計摘要:")
    print(f"   總檔案數: {len(cost_log)}")
    print(f"   總成本: ${total_cost:.4f}")
    print(f"   總API調用: {total_calls}")
    print(f"   總處理時間: {total_time:.2f}秒")
    print(f"   平均每檔案成本: ${total_cost/len(cost_log):.4f}")

    if optimized_entries:
        opt_cost = sum(e["total_cost"] for e in optimized_entries)
        opt_calls = sum(e["api_calls"] for e in optimized_entries)
        print(f"   優化版本 ({len(optimized_entries)}檔): ${opt_cost:.4f}, {opt_calls}次調用")

    if legacy_entries:
        leg_cost = sum(e["total_cost"] for e in legacy_entries)
        leg_calls = sum(e["api_calls"] for e in legacy_entries)
        print(f"   原始版本 ({len(legacy_entries)}檔): ${leg_cost:.4f}, {leg_calls}次調用")

async def main():
    print("🚀 批量上傳判決書到 Neo4j")
    print("=" * 50)

    files = list(SAMPLE_DIR.glob("*.json"))
    print(f"🔍 找到 {len(files)} 個檔案")

    # 顯示處理模式選項
    print("\n可用的處理模式:")
    print("1. 優化版本 (gpt-4o-mini + 兩階段提取) - 推薦")
    print("2. 原始版本 (gpt-4o + 六階段提取)")
    print("3. 自訂設定")
    print("4. 顯示成本統計")

    mode = input("\n請選擇處理模式 (1-4，預設1): ").strip() or "1"

    if mode == "4":
        print_cost_summary()
        return

    # 設定處理參數
    if mode == "1":
        use_optimized = True
        model = "gpt-4o-mini"
        print(f"✅ 使用優化版本 ({model})")
    elif mode == "2":
        use_optimized = False
        model = "gpt-4o"
        print(f"✅ 使用原始版本 ({model})")
    elif mode == "3":
        use_optimized = input("使用優化版本? (y/n，預設y): ").strip().lower() != "n"
        model = input("選擇模型 (gpt-4o-mini/gpt-4o，預設gpt-4o-mini): ").strip() or "gpt-4o-mini"
        print(f"✅ 自訂設定: {'優化' if use_optimized else '原始'}版本 ({model})")
    else:
        print("❌ 無效選項，使用預設設定")
        use_optimized = True
        model = "gpt-4o-mini"

    # 讀取進度檔
    if PROGRESS_FILE.exists():
        with PROGRESS_FILE.open("r", encoding="utf-8") as f:
            finished = set(json.load(f))
    else:
        finished = set()

    processed = 0
    skipped = 0
    failed = 0
    total_start_time = time.time()

    print(f"\n🎯 開始處理...")

    for i, file_path in enumerate(files, 1):
        print(f"\n[{i}/{len(files)}] ", end="")

        if file_path.name in finished:
            print(f"⏭️  已記錄完成，跳過: {file_path.name}")
            skipped += 1
            continue

        result = await process_and_upload(file_path, use_optimized=use_optimized, model=model)

        if result is False:  # 處理失敗或API配額不足
            failed += 1
            # 如果是API配額問題，停止處理
            if "insufficient_quota" in str(result) or "429" in str(result):
                print(f"\n⏸️  因 API 配額不足停止處理，已處理 {processed} 個檔案")
                break
        elif result is True:
            processed += 1
            finished.add(file_path.name)
            # 即時更新進度檔
            with PROGRESS_FILE.open("w", encoding="utf-8") as f:
                json.dump(list(finished), f, ensure_ascii=False, indent=2)

    total_time = time.time() - total_start_time

    print(f"\n📊 處理完成:")
    print(f"   ✅ 成功: {processed}")
    print(f"   ⏭️  跳過: {skipped}")
    print(f"   ❌ 失敗: {failed}")
    print(f"   ⏱️  總時間: {total_time:.2f}秒")

    if processed > 0:
        print(f"   📈 平均每檔案: {total_time/processed:.2f}秒")

    # 顯示成本統計
    print_cost_summary()

    driver.close()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  使用者中斷處理")
    except Exception as e:
        print(f"\n❌ 執行錯誤: {e}")
    finally:
        print("\n👋 程式結束")
