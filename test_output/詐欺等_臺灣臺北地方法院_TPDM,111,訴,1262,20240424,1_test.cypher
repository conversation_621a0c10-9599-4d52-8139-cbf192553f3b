MERGE (c:Case {case_id: 'TPDM,111,訴,1262,********,1'});

MATCH (c:Case {case_id: 'TPDM,111,訴,1262,********,1'}) SET c += {case_year: '111', judgment_year: '2024', judgment_month: '04', judgment_day: '24', case_type: '訴', case_number: '1262', title: '詐欺等', judgment_type: '無罪', judgment_date: '2024-04-24', court: '臺灣臺北地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/TPDM/111%2c%e8%a8%b4%2c1262%2c********%2c1.pdf', verdict_items: []};

MERGE (p0:Person {uid: 'TPDM,111,訴,1262,********,1_被告_1'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_被告_1'}) SET p += {name: '王遵富', role: '被告', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p1:Person {uid: 'TPDM,111,訴,1262,********,1_另案被告_1'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_另案被告_1'}) SET p += {name: '李峻鋠', role: '另案被告', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p2:Person {uid: 'TPDM,111,訴,1262,********,1_證人_1'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_1'}) SET p += {name: '李芳菲', role: '證人', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p3:Person {uid: 'TPDM,111,訴,1262,********,1_證人_2'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_2'}) SET p += {name: '陳韋廷', role: '證人', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p4:Person {uid: 'TPDM,111,訴,1262,********,1_證人_3'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_3'}) SET p += {name: '許家豪', role: '證人', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p5:Person {uid: 'TPDM,111,訴,1262,********,1_證人_4'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_4'}) SET p += {name: '吳素蓉', role: '證人', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p6:Person {uid: 'TPDM,111,訴,1262,********,1_證人_5'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_5'}) SET p += {name: '林毅君', role: '證人', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p7:Person {uid: 'TPDM,111,訴,1262,********,1_證人_6'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_6'}) SET p += {name: '鍾啟瑞', role: '證人', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p8:Person {uid: 'TPDM,111,訴,1262,********,1_證人_7'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_7'}) SET p += {name: '林家豪', role: '證人', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p9:Person {uid: 'TPDM,111,訴,1262,********,1_檢察官_1'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_檢察官_1'}) SET p += {name: '江文君', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p10:Person {uid: 'TPDM,111,訴,1262,********,1_檢察官_2'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_檢察官_2'}) SET p += {name: '林安紜', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p11:Person {uid: 'TPDM,111,訴,1262,********,1_檢察官_3'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_檢察官_3'}) SET p += {name: '戚瑛瑛', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p12:Person {uid: 'TPDM,111,訴,1262,********,1_法官_1'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_法官_1'}) SET p += {name: '解怡蕙', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p13:Person {uid: 'TPDM,111,訴,1262,********,1_法官_2'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_法官_2'}) SET p += {name: '楊世賢', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p14:Person {uid: 'TPDM,111,訴,1262,********,1_法官_3'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_法官_3'}) SET p += {name: '林奕宏', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p15:Person {uid: 'TPDM,111,訴,1262,********,1_書記官_1'});

MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_書記官_1'}) SET p += {name: '張閔翔', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (o0:Organization {name: '華南商業銀行'});

MATCH (o:Organization {name: '華南商業銀行'}) SET o.org_type = '銀行';

MERGE (o1:Organization {name: '僑馥建築經理股份有限公司'});

MATCH (o:Organization {name: '僑馥建築經理股份有限公司'}) SET o.org_type = '公司';

MERGE (o2:Organization {name: '亞緹公司'});

MATCH (o:Organization {name: '亞緹公司'}) SET o.org_type = '公司';

MERGE (l0:Location {name: '臺北市○○區○○街00號3樓'});

MATCH (l:Location {name: '臺北市○○區○○街00號3樓'}) SET l.type = '', l.address = '';

MERGE (l1:Location {name: '新北市○○區○○路000號有巢氏房屋新店中央央北店'});

MATCH (l:Location {name: '新北市○○區○○路000號有巢氏房屋新店中央央北店'}) SET l.type = '', l.address = '';

MERGE (l2:Location {name: '新北市○○區○○路000號華南商業銀行新莊分行'});

MATCH (l:Location {name: '新北市○○區○○路000號華南商業銀行新莊分行'}) SET l.type = '', l.address = '';

MERGE (l3:Location {name: '臺灣土地銀行信義分行'});

MATCH (l:Location {name: '臺灣土地銀行信義分行'}) SET l.type = '', l.address = '帳號000000000000';

MERGE (l4:Location {name: '臺北市基隆路亞緹公司之土地銀行'});

MATCH (l:Location {name: '臺北市基隆路亞緹公司之土地銀行'}) SET l.type = '', l.address = '';

MATCH (c:Case {case_id: 'TPDM,111,訴,1262,********,1'})
MERGE (law0:Law {article: '339條之4第1項第2款'})
SET law0.law_name = '刑法', law0.description = '三人以上共同犯詐欺取財'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'TPDM,111,訴,1262,********,1'})
MERGE (law1:Law {article: '216條'})
SET law1.law_name = '刑法', law1.description = '行使偽造私文書罪'
MERGE (c)-[:CITES_LAW]->(law1);

MATCH (c:Case {case_id: 'TPDM,111,訴,1262,********,1'})
MERGE (law2:Law {article: '210條'})
SET law2.law_name = '刑法', law2.description = '偽造私文書罪'
MERGE (c)-[:CITES_LAW]->(law2);

MATCH (c:Case {case_id: 'TPDM,111,訴,1262,********,1'})
MERGE (e0:Event {event_id: 'TPDM,111,訴,1262,********,1_ev1'})
SET e0.event_type = '詐欺取財', e0.date_time = '108年3月22日'
SET e0.amount = 1000000
SET e0.bank = '華南商業銀行'
SET e0.reason = '詐取房貸'
SET e0.method = '偽造不實買賣契約書'
SET e0.credibility = 'unknown'
SET e0.evidence_metrics = '不實買賣契約書'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: 'TPDM,111,訴,1262,********,1_ev1'})
MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'TPDM,111,訴,1262,********,1_ev1'})
MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_另案被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'TPDM,111,訴,1262,********,1_ev1'})
MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'TPDM,111,訴,1262,********,1_ev1'})
MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_3'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'TPDM,111,訴,1262,********,1_ev1'})
MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_4'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'TPDM,111,訴,1262,********,1_ev1'})
MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_5'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'TPDM,111,訴,1262,********,1_ev1'})
MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_1'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'TPDM,111,訴,1262,********,1_ev1'})
MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_4'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'TPDM,111,訴,1262,********,1_ev1'})
MATCH (p:Person {uid: 'TPDM,111,訴,1262,********,1_證人_5'})
MERGE (e)-[:TARGET]->(p);

MATCH (e:Event {event_id: 'TPDM,111,訴,1262,********,1_ev1'})
MATCH (l:Location {name: '臺北市○○區○○街00號3樓'})
MERGE (e)-[:IN_LOCATION]->(l);