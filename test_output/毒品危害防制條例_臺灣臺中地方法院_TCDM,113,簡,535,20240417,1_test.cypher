MERGE (c:Case {case_id: 'TCDM,113,簡,535,20240417,1'});

MATCH (c:Case {case_id: 'TCDM,113,簡,535,20240417,1'}) SET c += {case_year: '113', judgment_year: '2024', judgment_month: '04', judgment_day: '17', case_type: '簡', case_number: '535', title: '毒品危害防制條例', judgment_type: 'substantive', judgment_date: '2024-04-17', court: '臺灣臺中地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/TCDM/113%2c%e7%b0%a1%2c535%2c20240417%2c1.pdf', verdict_items: ['有期徒刑: 肆月，如易科罰金，以新臺幣壹仟元折算壹日']};

MERGE (p0:Person {uid: 'TCDM,113,簡,535,20240417,1_被告_1'});

MATCH (p:Person {uid: 'TCDM,113,簡,535,20240417,1_被告_1'}) SET p += {name: '陳建宏', role: '被告', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p1:Person {uid: 'TCDM,113,簡,535,20240417,1_檢察官_1'});

MATCH (p:Person {uid: 'TCDM,113,簡,535,20240417,1_檢察官_1'}) SET p += {name: '楊仕正', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p2:Person {uid: 'TCDM,113,簡,535,20240417,1_檢察官_2'});

MATCH (p:Person {uid: 'TCDM,113,簡,535,20240417,1_檢察官_2'}) SET p += {name: '王宜璇', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p3:Person {uid: 'TCDM,113,簡,535,20240417,1_法官_1'});

MATCH (p:Person {uid: 'TCDM,113,簡,535,20240417,1_法官_1'}) SET p += {name: '鄭永彬', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p4:Person {uid: 'TCDM,113,簡,535,20240417,1_書記官_1'});

MATCH (p:Person {uid: 'TCDM,113,簡,535,20240417,1_書記官_1'}) SET p += {name: '宋瑋陵', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (o0:Organization {name: '欣生生物科技股份有限公司'});

MATCH (o:Organization {name: '欣生生物科技股份有限公司'}) SET o.org_type = '公司';

MERGE (l0:Location {name: '臺中市大甲區文曲路某處之友人家中'});

MATCH (l:Location {name: '臺中市大甲區文曲路某處之友人家中'}) SET l.type = '', l.address = '';

MERGE (l1:Location {name: '臺中市○○區○○路000○0號'});

MATCH (l:Location {name: '臺中市○○區○○路000○0號'}) SET l.type = '', l.address = '';

MATCH (c:Case {case_id: 'TCDM,113,簡,535,20240417,1'})
MERGE (law0:Law {article: '第10條第2項'})
SET law0.law_name = '毒品危害防制條例', law0.description = '施用第二級毒品者，處3年以下有期徒刑。'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'TCDM,113,簡,535,20240417,1'})
MERGE (e0:Event {event_id: 'TCDM,113,簡,535,20240417,1_ev1'})
SET e0.event_type = '施用毒品', e0.date_time = '民國112年10月17日晚上7時10分'
SET e0.method = '以將甲基安非他命置入玻璃球內以火燒烤後吸食煙霧'
SET e0.confession = '陳建宏於本院審理中坦承不諱'
SET e0.credibility = 'high'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: 'TCDM,113,簡,535,20240417,1_ev1'})
MATCH (p:Person {uid: 'TCDM,113,簡,535,20240417,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'TCDM,113,簡,535,20240417,1_ev1'})
MATCH (l:Location {name: '臺中市大甲區文曲路某處之友人家中'})
MERGE (e)-[:IN_LOCATION]->(l);

MATCH (c:Case {case_id: 'TCDM,113,簡,535,20240417,1'})
MERGE (e1:Event {event_id: 'TCDM,113,簡,535,20240417,1_ev2'})
SET e1.event_type = '尿液檢驗', e1.date_time = '民國112年10月17日晚上7時10分'
SET e1.method = '採集尿液送驗'
SET e1.credibility = 'high'
SET e1.evidence_metrics = '檢驗結果呈甲基安非他命代謝後之安非他命、甲基安非他命陽性反應'
MERGE (c)-[:HAS_EVENT]->(e1);

MATCH (e:Event {event_id: 'TCDM,113,簡,535,20240417,1_ev2'})
MATCH (p:Person {uid: 'TCDM,113,簡,535,20240417,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'TCDM,113,簡,535,20240417,1_ev2'})
MATCH (l:Location {name: '臺中市○○區○○路000○0號'})
MERGE (e)-[:IN_LOCATION]->(l);