MERGE (c:Case {case_id: 'TPDM,113,原交簡上,3,20240328,1'});

MATCH (c:Case {case_id: 'TPDM,113,原交簡上,3,20240328,1'}) SET c += {case_year: '113', judgment_year: '2024', judgment_month: '03', judgment_day: '28', case_type: '原交簡上', case_number: '3', title: '公共危險', judgment_type: 'substantive', judgment_date: '2024-03-28', court: '臺灣臺北地方法院', pdf_url: 'https://data.judicial.gov.tw/opendl/JDocFile/TPDM/113%2c%e5%8e%9f%e4%ba%a4%e7%b0%a1%e4%b8%8a%2c3%2c20240328%2c1.pdf', verdict_items: ['有期徒刑: 處有期徒刑伍月，併科罰金新臺幣壹拾萬元']};

MERGE (p0:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_被告_1'});

MATCH (p:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_被告_1'}) SET p += {name: '鍾秉錀', role: '被告', gender: '男', relationship_to_others: '', is_anonymous: False};

MERGE (p1:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_辯護人_1'});

MATCH (p:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_辯護人_1'}) SET p += {name: '王世豪', role: '辯護人', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p2:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_檢察官_1'});

MATCH (p:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_檢察官_1'}) SET p += {name: '曾揚嶺', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p3:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_檢察官_2'});

MATCH (p:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_檢察官_2'}) SET p += {name: '李建論', role: '檢察官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p4:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_法官_1'});

MATCH (p:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_法官_1'}) SET p += {name: '唐玥', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p5:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_法官_2'});

MATCH (p:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_法官_2'}) SET p += {name: '魏小嵐', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p6:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_法官_3'});

MATCH (p:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_法官_3'}) SET p += {name: '邱于真', role: '法官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (p7:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_書記官_1'});

MATCH (p:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_書記官_1'}) SET p += {name: '林素霜', role: '書記官', gender: '', relationship_to_others: '', is_anonymous: False};

MERGE (l0:Location {name: '新北市樹林區樹新路某社區庭園'});

MATCH (l:Location {name: '新北市樹林區樹新路某社區庭園'}) SET l.type = '', l.address = '';

MERGE (l1:Location {name: '臺北市大安區辛亥路2段與建國南路2段口'});

MATCH (l:Location {name: '臺北市大安區辛亥路2段與建國南路2段口'}) SET l.type = '', l.address = '';

MATCH (c:Case {case_id: 'TPDM,113,原交簡上,3,20240328,1'})
MERGE (law0:Law {article: '185-3-1'})
SET law0.law_name = '中華民國刑法第185條之3', law0.description = '駕駛動力交通工具而有下列情形之一者，處3年以下有期徒刑，得併科30萬元以下罰金：一、吐氣所含酒精濃度達每公升零點二五毫克或血液中酒精濃度達百分之零點零五以上。'
MERGE (c)-[:CITES_LAW]->(law0);

MATCH (c:Case {case_id: 'TPDM,113,原交簡上,3,20240328,1'})
MERGE (law1:Law {article: '47-1'})
SET law1.law_name = '刑法第47條', law1.description = '對於累犯，應加重其刑。'
MERGE (c)-[:CITES_LAW]->(law1);

MATCH (c:Case {case_id: 'TPDM,113,原交簡上,3,20240328,1'})
MERGE (e0:Event {event_id: 'TPDM,113,原交簡上,3,20240328,1_ev1'})
SET e0.event_type = '酒後駕駛', e0.date_time = '2024-10-25 07:00'
SET e0.reason = '駕駛動力交通工具而有酒精濃度超標'
SET e0.method = '酒後駕駛'
SET e0.confession = '鍾秉錀於警詢及偵查中均坦承不諱'
SET e0.credibility = 'high'
SET e0.evidence_metrics = '吐氣酒精濃度達每公升0.56毫克'
MERGE (c)-[:HAS_EVENT]->(e0);

MATCH (e:Event {event_id: 'TPDM,113,原交簡上,3,20240328,1_ev1'})
MATCH (p:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_被告_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'TPDM,113,原交簡上,3,20240328,1_ev1'})
MATCH (p:Person {uid: 'TPDM,113,原交簡上,3,20240328,1_辯護人_1'})
MERGE (e)-[:HAS_PARTICIPANT]->(p);

MATCH (e:Event {event_id: 'TPDM,113,原交簡上,3,20240328,1_ev1'})
MATCH (l:Location {name: '新北市樹林區樹新路某社區庭園'})
MERGE (e)-[:IN_LOCATION]->(l);