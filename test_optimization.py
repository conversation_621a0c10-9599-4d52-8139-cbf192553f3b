#!/usr/bin/env python3
"""
判決書分析系統 - 成本優化測試腳本
測試兩階段合併調用 vs 原始6階段調用的成本差異
"""

import asyncio
import time
from pathlib import Path
from verdict_graph import (
    process_judgment, 
    test_cost_comparison,
    cost_tracker,
    process_judgment_optimized,
    process_judgment_legacy
)

async def quick_test():
    """快速測試單個檔案"""
    sample_dir = Path("sample_500")
    json_files = list(sample_dir.glob("*.json"))
    
    if not json_files:
        print("錯誤：找不到 sample_500 資料夾中的 JSON 檔案")
        return
    
    test_file = str(json_files[0])
    print(f"快速測試檔案: {test_file}")
    
    # 測試優化版本
    print("\n=== 測試優化版本 ===")
    start_time = time.time()
    result = await process_judgment_optimized(test_file)
    optimized_time = time.time() - start_time
    
    if result:
        print(f"✅ 優化版本測試成功")
        print(f"處理時間: {optimized_time:.2f} 秒")
        print(f"案件ID: {result.case.case_id}")
        print(f"提取人物數: {len(result.people)}")
        print(f"提取事件數: {len(result.events or [])}")
    else:
        print("❌ 優化版本測試失敗")

async def compare_accuracy():
    """準確度比較測試"""
    sample_dir = Path("sample_500")
    json_files = list(sample_dir.glob("*.json"))
    
    if not json_files:
        print("錯誤：找不到測試檔案")
        return
    
    test_file = str(json_files[0])
    print(f"準確度比較測試檔案: {test_file}")
    
    # 測試原始版本
    print("\n--- 原始版本 (GPT-4o + 6階段) ---")
    legacy_result = await process_judgment_legacy(test_file)
    
    # 測試優化版本  
    print("\n--- 優化版本 (GPT-4o-mini + 2階段) ---")
    optimized_result = await process_judgment_optimized(test_file)
    
    # 比較結果
    if legacy_result and optimized_result:
        print("\n=== 準確度比較 ===")
        print(f"原始版本 - 人物數: {len(legacy_result.people)}, 事件數: {len(legacy_result.events or [])}")
        print(f"優化版本 - 人物數: {len(optimized_result.people)}, 事件數: {len(optimized_result.events or [])}")
        
        # 比較人物提取
        legacy_people = {p.get('name', '') for p in legacy_result.people}
        optimized_people = {p.get('name', '') for p in optimized_result.people}
        
        people_overlap = len(legacy_people & optimized_people) / max(len(legacy_people), 1)
        print(f"人物提取重疊率: {people_overlap:.1%}")
        
        if people_overlap >= 0.8:
            print("✅ 準確度測試通過 (重疊率 >= 80%)")
        else:
            print("⚠️  準確度可能需要調整")

async def cost_projection():
    """成本推算"""
    print("=== 500個檔案成本推算 ===")
    
    # 基於測試結果的保守估算
    # GPT-4o 6次調用：約 $0.30 per file
    # GPT-4o-mini 2次調用：約 $0.06 per file
    
    legacy_cost_per_file = 0.30
    optimized_cost_per_file = 0.06
    
    legacy_total = legacy_cost_per_file * 500
    optimized_total = optimized_cost_per_file * 500
    savings = legacy_total - optimized_total
    savings_percent = (savings / legacy_total) * 100
    
    print(f"原始方案 (GPT-4o + 6階段): ${legacy_total:.2f}")
    print(f"優化方案 (GPT-4o-mini + 2階段): ${optimized_total:.2f}")
    print(f"預估節省: ${savings:.2f} ({savings_percent:.1f}%)")

def main():
    """主要測試流程"""
    print("判決書分析系統 - 成本優化測試")
    print("=" * 50)
    
    tests = {
        "1": ("快速測試", quick_test),
        "2": ("成本比較", lambda: test_cost_comparison(num_samples=2)),
        "3": ("準確度比較", compare_accuracy),
        "4": ("成本推算", cost_projection)
    }
    
    print("可用測試:")
    for key, (name, _) in tests.items():
        print(f"{key}. {name}")
    
    choice = input("\n請選擇測試 (1-4，預設1): ").strip() or "1"
    
    if choice in tests:
        test_name, test_func = tests[choice]
        print(f"\n執行 {test_name}...")
        
        if asyncio.iscoroutinefunction(test_func):
            asyncio.run(test_func())
        else:
            test_func()
    else:
        print("無效選擇，執行快速測試...")
        asyncio.run(quick_test())
    
    print("\n測試完成！")

if __name__ == "__main__":
    main()