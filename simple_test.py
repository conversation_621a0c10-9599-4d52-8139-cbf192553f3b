#!/usr/bin/env python3
"""
簡單測試腳本 - 驗證優化系統是否正常運行
"""

import asyncio
import json
from pathlib import Path

# 創建模擬 LLM 類別來測試工作流程
class MockLLM:
    def __init__(self):
        self.model = "mock-gpt-4o-mini"
    
    def with_structured_output(self, schema):
        return MockStructuredOutput(schema)

class MockStructuredOutput:
    def __init__(self, schema):
        self.schema = schema
    
    async def ainvoke(self, prompt):
        # 返回模擬數據
        from verdict_graph import Phase1Extract, Phase2Extract, CaseInfo, Person
        
        if self.schema == Phase1Extract:
            return Phase1Extract(
                case=CaseInfo(
                    case_id="TEST,113,交簡,1,20240101,1",
                    case_type="交簡",
                    judgment_type="substantive",
                    title="測試案件"
                ),
                people=[
                    Person(name="張三", role="被告"),
                    Person(name="李四", role="法官")
                ],
                organizations=[]
            )
        elif self.schema == Phase2Extract:
            return Phase2Extract(
                locations=[],
                laws=[],
                events=[]
            )

async def test_workflow():
    """測試工作流程是否正常運行"""
    try:
        from verdict_graph import create_workflow
        
        # 創建測試狀態
        test_content = json.dumps({
            "content": "這是一個測試判決書內容",
            "metadata": {"test": True}
        }, ensure_ascii=False)
        
        # 使用模擬 LLM
        mock_llm = MockLLM()
        
        # 測試優化版工作流程
        print("=== 測試兩階段優化工作流程 ===")
        workflow = create_workflow(use_optimized=True)
        
        result = await workflow.ainvoke({
            "content": test_content,
            "llm": mock_llm
        })
        
        print("✅ 兩階段優化工作流程測試成功")
        print(f"案件ID: {result['classification'].case.case_id}")
        print(f"人物數量: {len(result['classification'].people)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_loading():
    """測試檔案載入功能"""
    try:
        from verdict_graph import load_judgment_data
        
        # 檢查是否有測試檔案
        sample_dir = Path("sample_500")
        json_files = list(sample_dir.glob("*.json"))
        
        if not json_files:
            print("❌ 找不到測試檔案")
            return False
            
        test_file = json_files[0]
        print(f"測試載入檔案: {test_file}")
        
        doc = load_judgment_data(str(test_file))
        print("✅ 檔案載入測試成功")
        print(f"文件長度: {len(doc.page_content)} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 檔案載入測試失敗: {e}")
        return False

async def main():
    """主測試函數"""
    print("簡單測試腳本 - 驗證優化系統")
    print("=" * 40)
    
    # 測試 1: 檔案載入
    print("\n測試 1: 檔案載入功能")
    file_test = test_file_loading()
    
    # 測試 2: 工作流程
    print("\n測試 2: 工作流程執行")
    workflow_test = await test_workflow()
    
    # 總結
    print("\n=== 測試總結 ===")
    if file_test and workflow_test:
        print("🎉 所有測試通過！優化系統可以正常運行")
        print("\n下一步：")
        print("1. 設定 OpenAI API 金鑰")
        print("2. 執行 python verdict_graph.py 選擇模式 4 進行實際測試")
    else:
        print("⚠️  部分測試失敗，需要進一步檢查")

if __name__ == "__main__":
    asyncio.run(main())